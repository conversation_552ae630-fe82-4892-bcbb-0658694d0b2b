<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Superadmin\Consumer;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use LogicException;

abstract class Consumer
{

	public function __construct(
		private readonly ProductVariantRepository $productVariantRepository,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		//$this->productVariantRepository->setPublicOnly(false);

		return match ($class) {
			Product::class => $this->productVariantRepository->getById($objectId),
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

}
