<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Product;

use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductItem>
 */
final class ProductItemMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'order_product';
}

	public function getSalesDataFor6Month(Product $product): int
	{
		$variantIds = $product->variants->toCollection()->fetchPairs(null, 'id');

		if ($variantIds === []) {
			return 0;
		}

		$result = $this->connection->query('SELECT COUNT(*) from `order_product` AS op
										LEFT JOIN `order` AS o ON(op.orderId = o.id)
										WHERE variantId IN %i[] AND o.state != %s', $variantIds, OrderState::Canceled);
		return (int) $result->fetchField(0);
	}

	public function getSalesStats(int $productVariantId, array $daysBack = [30]): array
	{
		if (empty($productVariantId)) {
			return [];
		}

		$data = [];
		foreach ($daysBack as $days) {
			$data[$days] = ['amount' => 0, 'price_sum' => 0];
			$result = $this->connection->query('SELECT sum(amount) as amount, sum(amount * unitPrice_amount) as price_sum from `order_product` AS op
										LEFT JOIN `order` AS o ON(op.orderId = o.id)
										WHERE variantId = %i AND o.state != %s AND o.placedAt >= DATE_SUB(NOW(), INTERVAL %i DAY)', $productVariantId, OrderState::Canceled, $days);
			$result->fetch();
			if ($result->count() === 0) {
				$data[$days]['amount'] = (int)$result->amount;
				$data[$days]['price_sum'] = (float)$result->price_sum;
			}
		}

		return $data;
	}

}
