<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Product;

use App\Model\Orm\Product\Product;
use Nextras\Orm\Repository\Repository;

/**
 * @method int getSalesDataFor6Month(Product $product)
 * @method int getSalesStats(int $productVariantId, $daysBack = 30)
 * @extends Repository<ProductItem>
 */
final class ProductItemRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductItem::class];
	}

}
