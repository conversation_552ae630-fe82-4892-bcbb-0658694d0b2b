<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductBulk;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataSourceFactory;
use App\AdminModule\Presenters\ProductBulk\Components\Filter\FilterFormFactory;
use App\AdminModule\Presenters\ProductBulk\Components\Filter\FilterForm;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataGridFactory;
use App\Model\ElasticSearch\Superadmin\Convertor\ProductVariantData;
use App\Model\Orm\EsIndex\EsIndexRepository;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Responses\JsonResponse;
use Ublaboo\DataGrid\DataSource\ArrayDataSource;
use Ublaboo\DataGrid\DataSource\IDataSource;

final class ProductBulkPresenter extends BasePresenter
{

	#[Persistent]
	public array $filterSetup = [];

	private BoolQuery $boolQuery;

	private ?IDataSource $dataSource = null;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FilterFormFactory $filterFormFactory,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly DataSourceFactory $dataSourceFactory,
		//private readonly ProductNewActionFactory $productNewActionFactory,
		//private readonly ProductAddActionFactory $productAddActionFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(): void
	{
	}

	public function actionProductVariants(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$esDataSource = $this->getEsDataSource();
		foreach ($esDataSource->getData() as $item) {
			$responseData[] = ['value' => $item->id, 'label' => $item->name];
		}
		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function actionCategories(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$eshopTree = $this->orm->tree->getByUid("eshop", $this->mutationsHolder->getDefault());
		$trees = $this->orm->tree->searchByName($searchTerm, pathId: $eshopTree->id)
			->limitBy(10);

		foreach ($trees as $tree) {
			$responseData[] = ['value' => $tree->id, 'label' => $tree->name];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function actionTags(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$tags = $this->orm->tagLocalization->searchByName($searchTerm)
			->limitBy(10);

		foreach ($tags as $tag) {
			$responseData[] = ['value' => $tag->id, 'label' => $tag->name];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function renderDefault(): void
	{
		$this->template->hasFilter = $this->filterSetup !== [];
	}

	private function fillBoolQuery(): void
	{
		$data = $this->filterSetup;
		$b = new QueryBuilder();
		$this->boolQuery = $b->query()->bool();

		if (isset($data['eans']) && trim($data['eans']) !== '') {
			$eans = explode("\n", $data['eans']);
			if ($eans !== []) {
				$this->boolQuery->addMust(
					$b->query()->terms('ean')->setTerms($eans)
				);
			}
		}
		if (!empty($data['erpIds'])) {
			$erpIds = explode("\n", $data['erpIds']);
			if ($erpIds !== []) {
				$this->boolQuery->addMust(
					$b->query()->terms('erpCode')->setTerms($erpIds)
				);
			}
		}
		if (isset($data['productVariants']) && is_array($data['productVariants'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('id')->setTerms(array_values($data['productVariants']))
			);
		}
		if (isset($data['categories']) && is_array($data['categories'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('filter.categories')->setTerms(array_values($data['categories']))
			);
		}
		if (!empty($data['erpCategory'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('erpCategory')->setTerms([$data['erpCategory']])
			);
		}
		if (isset($data['tags']) && is_array($data['tags'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('bulkFilter.tagIds')->setTerms(array_values($data['tags']))
			);
		}
		if (isset($data['negate']) && (bool) $data['negate'] === true) {
			$this->boolQuery = $b->query()->bool()->addMustNot($this->boolQuery);
		}

		$this->boolQuery->addMust(
			$b->query()->term(['type' => ProductVariantData::KIND_PRODUCT_VARIANT])
		);
	}

	protected function createComponentFilterForm(): FilterForm
	{
		return $this->filterFormFactory->create($this->filterSetup);
	}

	protected function createComponentDataGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->getEsDataSource());
	}

	private function getEsDataSource():IDataSource {
		if ($this->dataSource === null) {
			$this->fillBoolQuery();
			$this->dataSource = new ArrayDataSource([]);
			$esIndex = $this->esIndexRepository->getSuperadminLastActive($this->mutationsHolder->getDefault());
			if ($esIndex !== null) {
				$this->dataSource = $this->dataSourceFactory->create($this->boolQuery, $esIndex);
			}
		}
		return $this->dataSource;
	}

	/*protected function createComponentProductNewAction(): ProductNewAction
	{
		assert($this->userEntity instanceof User);
		return $this->productNewActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}

	protected function createComponentProductAddAction(): ProductAddAction
	{
		assert($this->userEntity instanceof User);
		return $this->productAddActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}*/

}
